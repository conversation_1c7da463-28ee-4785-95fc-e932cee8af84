#!/usr/bin/env python3
"""
测试TOS上传功能
"""
import boto3
import base64
import os
import tempfile

# TOS配置
bucket_name = 'yunzhi-video'
endpoint_url = 'https://tos-s3-cn-shanghai.volces.com'
access_key = 'AKLTZmY3ZmQ2MWM3M2Q5NDY4M2FlMDgxNjRhM2MxZjAwZjY'
secret_key = 'TVdNMFlXWTFNamRtWlRsa05EQXhZemc0TW1Fell6Tm1PR1EwT0dVMk1XUQ=='

def test_tos_connection():
    """测试TOS连接"""
    try:
        # 尝试不同的secret key格式
        print(f"Access Key: {access_key}")

        # 方法1: 直接使用原始secret key
        print("尝试方法1: 直接使用原始secret key")
        try:
            client = boto3.client(
                's3',
                endpoint_url=endpoint_url,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                region_name='cn-shanghai'
            )
            buckets = client.list_buckets()
            print("方法1成功!")
        except Exception as e1:
            print(f"方法1失败: {e1}")

            # 方法2: base64解码
            print("尝试方法2: base64解码secret key")
            try:
                decoded_secret = base64.b64decode(secret_key).decode('utf-8')
                client = boto3.client(
                    's3',
                    endpoint_url=endpoint_url,
                    aws_access_key_id=access_key,
                    aws_secret_access_key=decoded_secret,
                    region_name='cn-shanghai'
                )
                buckets = client.list_buckets()
                print("方法2成功!")
            except Exception as e2:
                print(f"方法2失败: {e2}")
                raise e2
        
        # 测试连接 - 先尝试列出所有buckets
        try:
            buckets = client.list_buckets()
            print("TOS连接成功!")
            print(f"可访问的buckets: {[b['Name'] for b in buckets['Buckets']]}")
        except Exception as e:
            print(f"列出buckets失败: {e}")
            # 尝试直接访问指定bucket
            try:
                response = client.head_bucket(Bucket=bucket_name)
                print(f"Bucket {bucket_name} 存在且可访问")
            except Exception as e2:
                print(f"访问bucket {bucket_name} 失败: {e2}")
                raise e2
        
        # 创建测试文件
        test_content = "这是一个测试文件，用于验证TOS上传功能。"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            test_file_path = f.name
        
        try:
            # 上传测试文件
            test_key = "test/upload_test.txt"
            with open(test_file_path, 'rb') as file_data:
                client.upload_fileobj(
                    file_data,
                    bucket_name,
                    test_key,
                    ExtraArgs={'ContentType': 'text/plain'}
                )
            
            print(f"测试文件上传成功: {test_key}")
            
            # 验证文件存在
            response = client.head_object(Bucket=bucket_name, Key=test_key)
            print(f"文件大小: {response['ContentLength']} bytes")
            
            # 删除测试文件
            client.delete_object(Bucket=bucket_name, Key=test_key)
            print("测试文件已删除")
            
        finally:
            # 清理本地测试文件
            os.unlink(test_file_path)
        
        return True
        
    except Exception as e:
        print(f"TOS连接测试失败: {e}")
        return False

if __name__ == "__main__":
    test_tos_connection()
