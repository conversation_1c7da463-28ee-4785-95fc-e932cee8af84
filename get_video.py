import subprocess
import random
import string
import time
from datetime import datetime
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import threading

# 代理配置
PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = 7788
PROXY_USER = "5851b070f69-zone-adam"
PROXY_PASS = "t5gNbn29fwu2Rlhc"

# 视频列表文件
VIDEO_LIST_FILE = "/root/video/ytb/0823/id/YZ-20250823-Storyline-English-YTB_video.txt"

# 日志文件
SUCCESS_LOG = "success.log"
FAIL_LOG = "fail.log"

# 并发配置
MAX_WORKERS = 20 # 最大并发进程数
DELAY_BETWEEN_DOWNLOADS = 2  # 每个下载之间的延迟（秒）

# 用于线程安全的日志写入
log_lock = threading.Lock()


bucket_name = 'yunzhi-video'
endpoint_url = 'https://tos-s3-cn-shanghai.volces.com'
access_key = 'AKLTZmY3ZmQ2MWM3M2Q5NDY4M2FlMDgxNjRhM2MxZjAwZjY'
secret_key = 'TVdNMFlXWTFNamRtWlRsa05EQXhZemc0TW1Fell6Tm1PR1EwT0dVMk1XUQ=='


def write_log(log_file, message):
    """线程安全的日志写入"""
    with log_lock:
        with open(log_file, "a") as f:
            f.write(message)


def random_session(length=16):
    """生成随机 session 名称"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def download_video(url):
    """下载单个视频"""
    session_id = random_session()
    proxy_url = f"http://{PROXY_USER}-session-{session_id}:{PROXY_PASS}@{PROXY_HOST}:{PROXY_PORT}"

    cmd = [
        "yt-dlp",
        "-f", "bestvideo[height<=480]+bestaudio/best[height<=480]",
        "--write-info-json",
        "--add-metadata",  
        "--proxy", proxy_url,
        "-o", "./downloads/%(id)s_audio_video.%(ext)s",
        f"https://www.youtube.com/watch?v={url}" 
    ]

    try:
        subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        write_log(SUCCESS_LOG, f"{datetime.now()} | {url} | session={session_id} | OK\n")
        return True
    except subprocess.CalledProcessError:
        write_log(FAIL_LOG, f"{datetime.now()} | {url} | session={session_id} | FAIL\n")
        return False


def download_with_delay(url):
    """带延迟的下载函数，用于多进程调用"""
    # 随机延迟，避免所有进程同时开始
    time.sleep(random.uniform(0, DELAY_BETWEEN_DOWNLOADS))
    return download_video(url)


def main_sequential():
    """原始的串行下载方式"""
    with open(VIDEO_LIST_FILE, "r") as f:
        urls = [line.strip() for line in f if line.strip()]

    print(f"开始串行下载，共 {len(urls)} 个视频...")
    success, fail = 0, 0

    for url in urls:
        if download_video(url):
            success += 1
        else:
            fail += 1
        time.sleep(5)  # 防止过快触发限制

    print(f"\n下载完成 {success} 成功, {fail} 失败")


def main_concurrent():
    """多进程并发下载方式"""
    with open(VIDEO_LIST_FILE, "r") as f:
        urls = [line.strip() for line in f if line.strip()]

    print(f"开始并发下载，共 {len(urls)} 个视频，使用 {MAX_WORKERS} 个进程...")
    success, fail = 0, 0

    with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 提交所有任务
        future_to_url = {executor.submit(download_with_delay, url): url for url in urls}

        # 处理完成的任务
        for future in as_completed(future_to_url):
            url = future_to_url[future]
            try:
                result = future.result()
                if result:
                    success += 1
                    print(f"成功下载: {url}")
                else:
                    fail += 1
                    print(f"下载失败: {url}")
            except Exception as exc:
                fail += 1
                print(f"下载异常: {url} - {exc}")
                write_log(FAIL_LOG, f"{datetime.now()} | {url} | EXCEPTION: {exc}\n")

    print(f"\n并发下载完成 {success} 成功, {fail} 失败")


def main():
    """主函数，可以选择串行或并发模式"""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--sequential":
        print("使用串行下载模式")
        main_sequential()
    else:
        print("使用并发下载模式")
        main_concurrent()


if __name__ == "__main__":
    main()
