import subprocess
import random
import string
import time
from datetime import datetime
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import threading
import signal
import sys
import os
import psutil
import boto3
import base64
import glob
import json

# 代理配置
PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = 7788
PROXY_USER = "5851b070f69-zone-adam"
PROXY_PASS = "t5gNbn29fwu2Rlhc"

# 视频列表文件
VIDEO_LIST_FILE = "/root/video/ytb/0823/id/YZ-20250823-Storyline-English-YTB_video.txt"
# 如果原文件不存在，使用测试文件
if not os.path.exists(VIDEO_LIST_FILE):
    VIDEO_LIST_FILE = "test_videos.txt"

# 日志文件
SUCCESS_LOG = "success.log"
FAIL_LOG = "fail.log"

# 并发配置
MAX_WORKERS = 20 # 最大并发进程数
DELAY_BETWEEN_DOWNLOADS = 2  # 每个下载之间的延迟（秒）

# 用于线程安全的日志写入
log_lock = threading.Lock()

# 全局变量用于进程管理
executor = None
running_processes = set()
shutdown_event = threading.Event()

# 统计信息
stats_lock = threading.Lock()
total_downloaded_size = 0
total_download_time = 0
total_files_processed = 0


bucket_name = 'yunzhi-video'
endpoint_url = 'https://tos-s3-cn-shanghai.volces.com'
access_key = 'AKLTZmY3ZmQ2MWM3M2Q5NDY4M2FlMDgxNjRhM2MxZjAwZjY'
secret_key = 'TVdNMFlXWTFNamRtWlRsa05EQXhZemc0TW1Fell6Tm1PR1EwT0dVMk1XUQ=='

# TOS客户端
tos_client = None


def init_tos_client():
    """初始化TOS客户端"""
    global tos_client
    try:
        # 解码secret_key
        decoded_secret = base64.b64decode(secret_key).decode('utf-8')

        tos_client = boto3.client(
            's3',
            endpoint_url=endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=decoded_secret,
            region_name='cn-shanghai'
        )
        print("TOS客户端初始化成功")
        return True
    except Exception as e:
        print(f"TOS客户端初始化失败: {e}")
        return False


def signal_handler(signum, frame):
    """信号处理函数，用于优雅地关闭程序"""
    print(f"\n收到信号 {signum}，正在关闭程序...")
    shutdown_event.set()

    # 关闭进程池
    global executor
    if executor:
        print("正在关闭进程池...")
        executor.shutdown(wait=False)

    # 终止所有运行中的子进程
    cleanup_processes()

    print("程序已关闭")
    sys.exit(0)


def cleanup_processes():
    """清理所有运行中的子进程"""
    current_process = psutil.Process()
    children = current_process.children(recursive=True)

    for child in children:
        try:
            print(f"终止子进程 {child.pid}")
            child.terminate()
        except psutil.NoSuchProcess:
            pass

    # 等待进程终止
    gone, alive = psutil.wait_procs(children, timeout=3)

    # 强制杀死仍然存活的进程
    for p in alive:
        try:
            print(f"强制杀死进程 {p.pid}")
            p.kill()
        except psutil.NoSuchProcess:
            pass


def write_log(log_file, message):
    """线程安全的日志写入"""
    with log_lock:
        with open(log_file, "a") as f:
            f.write(message)


def upload_to_tos(local_file_path, video_id):
    """上传文件到TOS"""
    if not tos_client:
        print("TOS客户端未初始化")
        return False

    try:
        # 获取文件名和扩展名
        filename = os.path.basename(local_file_path)
        file_extension = os.path.splitext(filename)[1]

        # 构造TOS中的对象键
        tos_key = f"youtube_videos/{video_id}/{filename}"

        # 上传文件
        print(f"正在上传 {filename} 到TOS...")
        with open(local_file_path, 'rb') as file_data:
            tos_client.upload_fileobj(
                file_data,
                bucket_name,
                tos_key,
                ExtraArgs={
                    'ContentType': 'video/mp4' if file_extension in ['.mp4', '.webm', '.mkv'] else 'application/json'
                }
            )

        print(f"成功上传: {tos_key}")
        return True

    except Exception as e:
        print(f"上传失败 {local_file_path}: {e}")
        return False


def cleanup_local_files(video_id):
    """清理本地下载的文件"""
    try:
        # 查找所有相关文件
        pattern = f"./downloads/{video_id}_*"
        files_to_delete = glob.glob(pattern)

        for file_path in files_to_delete:
            try:
                os.remove(file_path)
                print(f"删除本地文件: {file_path}")
            except Exception as e:
                print(f"删除文件失败 {file_path}: {e}")

        return len(files_to_delete) > 0

    except Exception as e:
        print(f"清理本地文件时出错: {e}")
        return False


def update_stats(size_mb, time_taken):
    """更新全局统计信息"""
    global total_downloaded_size, total_download_time, total_files_processed
    with stats_lock:
        total_downloaded_size += size_mb
        total_download_time += time_taken
        total_files_processed += 1


def print_stats():
    """打印统计信息"""
    with stats_lock:
        if total_files_processed > 0:
            avg_speed = total_downloaded_size / total_download_time if total_download_time > 0 else 0
            print(f"\n=== 吞吐量统计 ===")
            print(f"处理文件数: {total_files_processed}")
            print(f"总下载大小: {total_downloaded_size:.2f} MB")
            print(f"总处理时间: {total_download_time:.2f} 秒")
            print(f"平均速度: {avg_speed:.2f} MB/s")
            print(f"平均每文件: {total_downloaded_size/total_files_processed:.2f} MB")
            print(f"平均每文件时间: {total_download_time/total_files_processed:.2f} 秒")
        else:
            print("没有成功处理的文件")


def random_session(length=16):
    """生成随机 session 名称"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def download_video(url):
    """下载单个视频"""
    # 检查是否收到关闭信号
    if shutdown_event.is_set():
        return False

    start_time = time.time()
    session_id = random_session()
    proxy_url = f"http://{PROXY_USER}-session-{session_id}:{PROXY_PASS}@{PROXY_HOST}:{PROXY_PORT}"

    cmd = [
        "yt-dlp",
        "-f", "bestvideo[height>=1080]+bestaudio/best",
        "--write-info-json",
        "--add-metadata",
        "--proxy", proxy_url,
        "-o", "./downloads/%(id)s_audio_video.%(ext)s",
        f"https://www.youtube.com/watch?v={url}"
    ]

    process = None
    try:
        # 启动子进程并跟踪
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        running_processes.add(process.pid)

        # 等待进程完成，同时检查关闭信号
        while process.poll() is None:
            if shutdown_event.is_set():
                print(f"中断下载: {url}")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                return False
            time.sleep(0.1)

        # 进程完成，从跟踪集合中移除
        running_processes.discard(process.pid)

        if process.returncode == 0:
            total_time = time.time() - start_time

            # 查找下载的文件
            pattern = f"./downloads/{url}_*"
            downloaded_files = glob.glob(pattern)

            if not downloaded_files:
                write_log(FAIL_LOG, f"{datetime.now()} | {url} | session={session_id} | NO_FILES_FOUND\n")
                return False

            # 计算文件大小
            total_size = sum(os.path.getsize(f) for f in downloaded_files)
            size_mb = total_size / (1024 * 1024)

            if tos_client:
                # TOS可用，尝试上传
                print(f"下载完成: {url}，开始上传到TOS...")

                upload_success = True
                uploaded_files = []

                for file_path in downloaded_files:
                    if upload_to_tos(file_path, url):
                        uploaded_files.append(file_path)
                    else:
                        upload_success = False
                        break

                if upload_success and uploaded_files:
                    # 上传成功，删除本地文件
                    cleanup_local_files(url)

                    # 更新统计信息
                    update_stats(size_mb, total_time)

                    write_log(SUCCESS_LOG, f"{datetime.now()} | {url} | session={session_id} | DOWNLOAD_UPLOAD_OK | files={len(uploaded_files)} | size={size_mb:.2f}MB | time={total_time:.2f}s | speed={size_mb/total_time:.2f}MB/s\n")
                    print(f"完成处理: {url} (下载+上传+清理) - {size_mb:.2f}MB in {total_time:.2f}s ({size_mb/total_time:.2f}MB/s)")
                    return True
                else:
                    # 上传失败，保留本地文件
                    write_log(FAIL_LOG, f"{datetime.now()} | {url} | session={session_id} | UPLOAD_FAIL | time={total_time:.2f}s\n")
                    print(f"上传失败: {url}，保留本地文件")
                    return False
            else:
                # TOS不可用，仅测试下载
                print(f"下载完成: {url} (仅下载测试，TOS不可用)")

                # 更新统计信息
                update_stats(size_mb, total_time)

                write_log(SUCCESS_LOG, f"{datetime.now()} | {url} | session={session_id} | DOWNLOAD_ONLY_OK | files={len(downloaded_files)} | size={size_mb:.2f}MB | time={total_time:.2f}s | speed={size_mb/total_time:.2f}MB/s\n")
                print(f"完成下载: {url} - {size_mb:.2f}MB in {total_time:.2f}s ({size_mb/total_time:.2f}MB/s)")
                return True
        else:
            write_log(FAIL_LOG, f"{datetime.now()} | {url} | session={session_id} | DOWNLOAD_FAIL\n")
            return False

    except Exception as e:
        if process:
            running_processes.discard(process.pid)
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        write_log(FAIL_LOG, f"{datetime.now()} | {url} | session={session_id} | EXCEPTION: {e}\n")
        return False


def download_with_delay(url):
    """带延迟的下载函数，用于多进程调用"""
    # 随机延迟，避免所有进程同时开始
    time.sleep(random.uniform(0, DELAY_BETWEEN_DOWNLOADS))
    return download_video(url)


def main_sequential():
    """原始的串行下载方式"""
    with open(VIDEO_LIST_FILE, "r") as f:
        urls = [line.strip() for line in f if line.strip()]

    print(f"开始串行下载，共 {len(urls)} 个视频...")
    success, fail = 0, 0

    try:
        for url in urls:
            # 检查是否收到关闭信号
            if shutdown_event.is_set():
                print("收到关闭信号，停止下载...")
                break

            if download_video(url):
                success += 1
            else:
                fail += 1

            # 在延迟期间也检查关闭信号
            for _ in range(50):  # 5秒分成50个0.1秒检查
                if shutdown_event.is_set():
                    break
                time.sleep(0.1)

    except KeyboardInterrupt:
        print("\n收到键盘中断信号...")
        shutdown_event.set()

    print(f"\n下载完成 {success} 成功, {fail} 失败")
    print_stats()


def main_concurrent():
    """多进程并发下载方式"""
    global executor

    with open(VIDEO_LIST_FILE, "r") as f:
        urls = [line.strip() for line in f if line.strip()]

    print(f"开始并发下载，共 {len(urls)} 个视频，使用 {MAX_WORKERS} 个进程...")
    success, fail = 0, 0

    try:
        executor = ProcessPoolExecutor(max_workers=MAX_WORKERS)

        # 提交所有任务
        future_to_url = {executor.submit(download_with_delay, url): url for url in urls}

        # 处理完成的任务
        for future in as_completed(future_to_url):
            # 检查是否收到关闭信号
            if shutdown_event.is_set():
                print("收到关闭信号，停止处理新任务...")
                break

            url = future_to_url[future]
            try:
                result = future.result(timeout=1)  # 添加超时以便能响应中断
                if result:
                    success += 1
                    print(f"成功下载: {url}")
                else:
                    fail += 1
                    print(f"下载失败: {url}")
            except Exception as exc:
                fail += 1
                print(f"下载异常: {url} - {exc}")
                write_log(FAIL_LOG, f"{datetime.now()} | {url} | EXCEPTION: {exc}\n")

    except KeyboardInterrupt:
        print("\n收到键盘中断信号...")
        shutdown_event.set()
    finally:
        if executor:
            print("正在关闭进程池...")
            executor.shutdown(wait=False)
            executor = None

    print(f"\n并发下载完成 {success} 成功, {fail} 失败")
    print_stats()


def main():
    """主函数，可以选择串行或并发模式"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 初始化TOS客户端
    tos_available = init_tos_client()
    if not tos_available:
        print("TOS客户端初始化失败，将跳过上传步骤，仅进行下载测试")
        global tos_client
        tos_client = None

    # 确保下载目录存在
    os.makedirs("./downloads", exist_ok=True)

    try:
        if len(sys.argv) > 1 and sys.argv[1] == "--sequential":
            print("使用串行下载模式")
            main_sequential()
        else:
            print("使用并发下载模式")
            main_concurrent()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"程序发生错误: {e}")
        cleanup_processes()
        sys.exit(1)


if __name__ == "__main__":
    main()
