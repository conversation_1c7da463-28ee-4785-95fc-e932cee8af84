#!/usr/bin/env python3
"""
使用原生TOS SDK测试连接
"""
import tos
import base64
import os
import tempfile

# TOS配置
bucket_name = 'yunzhi-video'
# TOS原生endpoint (不是S3兼容的)
endpoint_url = 'https://tos-cn-shanghai.volces.com'
access_key = 'AKLTZmY3ZmQ2MWM3M2Q5NDY4M2FlMDgxNjRhM2MxZjAwZjY'
secret_key = 'TVdNMFlXWTFNamRtWlRsa05EQXhZemc0TW1Fell6Tm1PR1EwT0dVMk1XUQ=='

def test_tos_native():
    """使用原生TOS SDK测试连接"""
    try:
        # 尝试不同的配置
        configs = [
            {"secret": secret_key, "desc": "原始secret key"},
            {"secret": base64.b64decode(secret_key).decode('utf-8'), "desc": "base64解码secret key"}
        ]
        
        client = None
        for i, config in enumerate(configs):
            print(f"尝试配置 {i+1}: {config['desc']}")
            try:
                # 创建TOS客户端 - 尝试不同的参数名
                try:
                    client = tos.TosClientV2(
                        region='cn-shanghai',
                        access_key_id=access_key,
                        access_key_secret=config['secret'],
                        endpoint=endpoint_url
                    )
                except TypeError:
                    # 尝试其他参数名
                    try:
                        client = tos.TosClientV2(
                            region='cn-shanghai',
                            ak=access_key,
                            sk=config['secret'],
                            endpoint=endpoint_url
                        )
                    except TypeError:
                        # 再尝试其他可能的参数名
                        client = tos.TosClientV2(
                            region='cn-shanghai',
                            access_key=access_key,
                            secret_key=config['secret'],
                            endpoint=endpoint_url
                        )
                
                # 测试连接 - 先尝试列出buckets
                try:
                    result = client.list_buckets()
                    print(f"配置 {i+1} 成功! (list_buckets)")
                    print(f"找到 {len(result.buckets)} 个buckets:")
                    for bucket in result.buckets:
                        print(f"  - {bucket.name}")
                    break
                except Exception as list_error:
                    print(f"list_buckets失败: {list_error}")
                    # 尝试直接访问指定bucket
                    try:
                        result = client.head_bucket(bucket=bucket_name)
                        print(f"配置 {i+1} 成功! (head_bucket)")
                        print(f"可以访问bucket: {bucket_name}")
                        break
                    except Exception as head_error:
                        print(f"head_bucket也失败: {head_error}")
                        raise list_error
                
            except Exception as e:
                print(f"配置 {i+1} 失败: {e}")
                client = None
        
        if not client:
            raise Exception("所有配置都失败了")
        
        # 测试上传
        print(f"\n测试上传到bucket: {bucket_name}")
        
        # 创建测试文件
        test_content = "这是一个TOS原生SDK测试文件"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            test_file_path = f.name
        
        try:
            # 上传测试文件
            test_key = "test/native_sdk_test.txt"
            with open(test_file_path, 'rb') as file_data:
                result = client.put_object(
                    bucket=bucket_name,
                    key=test_key,
                    content=file_data
                )
            
            print(f"测试文件上传成功: {test_key}")
            print(f"ETag: {result.etag}")
            
            # 验证文件存在
            head_result = client.head_object(bucket=bucket_name, key=test_key)
            print(f"文件大小: {head_result.content_length} bytes")
            
            # 删除测试文件
            client.delete_object(bucket=bucket_name, key=test_key)
            print("测试文件已删除")
            
        finally:
            # 清理本地测试文件
            os.unlink(test_file_path)
        
        return True
        
    except Exception as e:
        print(f"TOS原生SDK测试失败: {e}")
        return False

if __name__ == "__main__":
    test_tos_native()
