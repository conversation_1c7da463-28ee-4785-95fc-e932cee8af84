#!/usr/bin/env python3
"""
测试脚本：验证中断处理是否正常工作
"""
import subprocess
import time
import signal
import sys
import os

def test_interrupt():
    """测试中断处理"""
    print("启动 get_video.py 进程...")
    print("请在几秒后按 Ctrl+C 来测试中断处理")
    
    # 创建一个简单的测试视频列表
    test_video_file = "test_videos.txt"
    with open(test_video_file, "w") as f:
        f.write("dQw4w9WgXcQ\n")  # Rick Roll video ID for testing
        f.write("9bZkp7q19f0\n")  # Another test video
    
    try:
        # 修改 get_video.py 中的视频列表文件路径进行测试
        process = subprocess.Popen([
            sys.executable, "get_video.py", "--sequential"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # 等待一段时间然后发送中断信号
        time.sleep(5)
        print("\n发送 SIGINT 信号...")
        process.send_signal(signal.SIGINT)
        
        # 等待进程结束
        try:
            stdout, _ = process.communicate(timeout=10)
            print("进程输出:")
            print(stdout)
        except subprocess.TimeoutExpired:
            print("进程没有在预期时间内结束，强制终止...")
            process.kill()
            stdout, _ = process.communicate()
            print("进程输出:")
            print(stdout)
        
        print(f"进程返回码: {process.returncode}")
        
    finally:
        # 清理测试文件
        if os.path.exists(test_video_file):
            os.remove(test_video_file)

if __name__ == "__main__":
    test_interrupt()
